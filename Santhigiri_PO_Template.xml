<?xml version="1.0"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Title>Santhigiri Ashram Purchase Order</Title>
  <Subject>Purchase Order Template</Subject>
  <Author>Santhigiri Ashram</Author>
  <Created>2024-01-01T00:00:00Z</Created>
 </DocumentProperties>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>12000</WindowHeight>
  <WindowWidth>18000</WindowWidth>
  <WindowTopX>0</WindowTopX>
  <WindowTopY>0</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="Arial" x:Family="Swiss" ss:Size="10"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s62">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="2"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="2"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="2"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="2"/>
   </Borders>
   <Font ss:FontName="Arial" x:Family="Swiss" ss:Size="16" ss:Bold="1"/>
   <Interior ss:Color="#E6E6E6" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s63">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Font ss:FontName="Arial" x:Family="Swiss" ss:Size="12" ss:Bold="1"/>
  </Style>
  <Style ss:ID="s64">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Arial" x:Family="Swiss" ss:Size="10" ss:Bold="1"/>
   <Interior ss:Color="#F0F0F0" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s65">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
  </Style>
  <Style ss:ID="s66">
   <Alignment ss:Horizontal="Right" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <NumberFormat ss:Format="₹#,##0.00"/>
  </Style>
 </Styles>
 <Worksheet ss:Name="Purchase Order">
  <Table ss:ExpandedColumnCount="8" ss:ExpandedRowCount="50" x:FullColumns="1" x:FullRows="1">
   <Column ss:AutoFitWidth="0" ss:Width="60"/>
   <Column ss:AutoFitWidth="0" ss:Width="120"/>
   <Column ss:AutoFitWidth="0" ss:Width="200"/>
   <Column ss:AutoFitWidth="0" ss:Width="80"/>
   <Column ss:AutoFitWidth="0" ss:Width="80"/>
   <Column ss:AutoFitWidth="0" ss:Width="100"/>
   <Column ss:AutoFitWidth="0" ss:Width="100"/>
   <Column ss:AutoFitWidth="0" ss:Width="80"/>
   
   <!-- Header Section -->
   <Row ss:Height="25">
    <Cell ss:MergeAcross="7" ss:StyleID="s62">
     <Data ss:Type="String">SANTHIGIRI ASHRAM</Data>
    </Cell>
   </Row>
   <Row ss:Height="15">
    <Cell ss:MergeAcross="7" ss:StyleID="s63">
     <Data ss:Type="String">Santhigiri Ashram, Pothencode P.O., Thiruvananthapuram - 695 584, Kerala, India</Data>
    </Cell>
   </Row>
   <Row ss:Height="15">
    <Cell ss:MergeAcross="7" ss:StyleID="s63">
     <Data ss:Type="String">Phone: +91-471-2274999 | Email: <EMAIL> | Website: www.santhigiri.org</Data>
    </Cell>
   </Row>
   <Row ss:Height="15">
    <Cell ss:MergeAcross="7" ss:StyleID="s63">
     <Data ss:Type="String">GSTIN: [GST Number] | PAN: [PAN Number]</Data>
    </Cell>
   </Row>
   <Row ss:Height="5"/>
   <Row ss:Height="20">
    <Cell ss:MergeAcross="7" ss:StyleID="s62">
     <Data ss:Type="String">PURCHASE ORDER</Data>
    </Cell>
   </Row>
   <Row ss:Height="5"/>
   
   <!-- Vendor and Order Details -->
   <Row ss:Height="18">
    <Cell ss:MergeAcross="3" ss:StyleID="s64">
     <Data ss:Type="String">VENDOR DETAILS</Data>
    </Cell>
    <Cell ss:MergeAcross="3" ss:StyleID="s64">
     <Data ss:Type="String">ORDER DETAILS</Data>
    </Cell>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"><Data ss:Type="String">Vendor Name:</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"><Data ss:Type="String">PO Number:</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"><Data ss:Type="String">Address:</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"><Data ss:Type="String">PO Date:</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"/>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"><Data ss:Type="String">Required Date:</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"><Data ss:Type="String">Phone:</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"><Data ss:Type="String">Department:</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"><Data ss:Type="String">Email:</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"><Data ss:Type="String">Requested By:</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"><Data ss:Type="String">GSTIN:</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"><Data ss:Type="String">Budget Code:</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="5"/>
   
   <!-- Delivery Information -->
   <Row ss:Height="18">
    <Cell ss:MergeAcross="7" ss:StyleID="s64">
     <Data ss:Type="String">DELIVERY INFORMATION</Data>
    </Cell>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"><Data ss:Type="String">Delivery Address:</Data></Cell>
    <Cell ss:MergeAcross="3" ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"><Data ss:Type="String">Delivery Date:</Data></Cell>
    <Cell ss:MergeAcross="1" ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"/>
    <Cell ss:MergeAcross="3" ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"><Data ss:Type="String">Delivery Time:</Data></Cell>
    <Cell ss:MergeAcross="1" ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"><Data ss:Type="String">Contact Person:</Data></Cell>
    <Cell ss:MergeAcross="3" ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"><Data ss:Type="String">Special Instructions:</Data></Cell>
    <Cell ss:MergeAcross="1" ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="5"/>
   
   <!-- Items Table Header -->
   <Row ss:Height="18">
    <Cell ss:StyleID="s64"><Data ss:Type="String">S.No</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">Item Code</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">Description</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">Unit</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">Qty</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">Rate</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">Amount</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">Tax%</Data></Cell>
   </Row>
   
   <!-- Items Rows -->
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"><Data ss:Type="Number">1</Data></Cell>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s66"/>
    <Cell ss:StyleID="s66"/>
    <Cell ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"><Data ss:Type="Number">2</Data></Cell>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s66"/>
    <Cell ss:StyleID="s66"/>
    <Cell ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"><Data ss:Type="Number">3</Data></Cell>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s66"/>
    <Cell ss:StyleID="s66"/>
    <Cell ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"><Data ss:Type="Number">4</Data></Cell>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s66"/>
    <Cell ss:StyleID="s66"/>
    <Cell ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:StyleID="s65"><Data ss:Type="Number">5</Data></Cell>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s65"/>
    <Cell ss:StyleID="s66"/>
    <Cell ss:StyleID="s66"/>
    <Cell ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="5"/>
   
   <!-- Totals Section -->
   <Row ss:Height="15">
    <Cell ss:Index="6" ss:StyleID="s64"><Data ss:Type="String">Sub Total:</Data></Cell>
    <Cell ss:StyleID="s66"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:Index="6" ss:StyleID="s64"><Data ss:Type="String">Discount:</Data></Cell>
    <Cell ss:StyleID="s66"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:Index="6" ss:StyleID="s64"><Data ss:Type="String">Taxable Amount:</Data></Cell>
    <Cell ss:StyleID="s66"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:Index="6" ss:StyleID="s64"><Data ss:Type="String">CGST:</Data></Cell>
    <Cell ss:StyleID="s66"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:Index="6" ss:StyleID="s64"><Data ss:Type="String">SGST:</Data></Cell>
    <Cell ss:StyleID="s66"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:Index="6" ss:StyleID="s64"><Data ss:Type="String">IGST:</Data></Cell>
    <Cell ss:StyleID="s66"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:Index="6" ss:StyleID="s62"><Data ss:Type="String">TOTAL:</Data></Cell>
    <Cell ss:StyleID="s62"/>
   </Row>
   <Row ss:Height="10"/>
   
   <!-- Terms and Conditions -->
   <Row ss:Height="18">
    <Cell ss:MergeAcross="7" ss:StyleID="s64">
     <Data ss:Type="String">TERMS AND CONDITIONS</Data>
    </Cell>
   </Row>
   <Row ss:Height="15">
    <Cell ss:MergeAcross="7" ss:StyleID="s65">
     <Data ss:Type="String">1. Payment Terms: _______________  2. Delivery Terms: _______________  3. Warranty: _______________</Data>
    </Cell>
   </Row>
   <Row ss:Height="15">
    <Cell ss:MergeAcross="7" ss:StyleID="s65">
     <Data ss:Type="String">4. Quality Standards: As per specifications  5. Inspection: Subject to our inspection</Data>
    </Cell>
   </Row>
   <Row ss:Height="15">
    <Cell ss:MergeAcross="7" ss:StyleID="s65">
     <Data ss:Type="String">6. Returns: Defective items returnable  7. Jurisdiction: Thiruvananthapuram</Data>
    </Cell>
   </Row>
   <Row ss:Height="10"/>
   
   <!-- Signatures -->
   <Row ss:Height="18">
    <Cell ss:MergeAcross="2" ss:StyleID="s64">
     <Data ss:Type="String">PREPARED BY</Data>
    </Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s64">
     <Data ss:Type="String">APPROVED BY</Data>
    </Cell>
    <Cell ss:MergeAcross="1" ss:StyleID="s64">
     <Data ss:Type="String">AUTHORIZED SIGNATORY</Data>
    </Cell>
   </Row>
   <Row ss:Height="30">
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
    <Cell ss:MergeAcross="2" ss:StyleID="s65"/>
    <Cell ss:MergeAcross="1" ss:StyleID="s65"/>
   </Row>
   <Row ss:Height="15">
    <Cell ss:MergeAcross="2" ss:StyleID="s65">
     <Data ss:Type="String">Name: _______________</Data>
    </Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65">
     <Data ss:Type="String">Name: _______________</Data>
    </Cell>
    <Cell ss:MergeAcross="1" ss:StyleID="s65">
     <Data ss:Type="String">Name: _______________</Data>
    </Cell>
   </Row>
   <Row ss:Height="15">
    <Cell ss:MergeAcross="2" ss:StyleID="s65">
     <Data ss:Type="String">Date: _______________</Data>
    </Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="s65">
     <Data ss:Type="String">Date: _______________</Data>
    </Cell>
    <Cell ss:MergeAcross="1" ss:StyleID="s65">
     <Data ss:Type="String">Date: _______________</Data>
    </Cell>
   </Row>
  </Table>
  <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
   <PageSetup>
    <Header x:Margin="0.3"/>
    <Footer x:Margin="0.3"/>
    <PageMargins x:Bottom="0.75" x:Left="0.7" x:Right="0.7" x:Top="0.75"/>
   </PageSetup>
   <Print>
    <ValidPrinterInfo/>
    <PaperSizeIndex>9</PaperSizeIndex>
    <HorizontalResolution>600</HorizontalResolution>
    <VerticalResolution>600</VerticalResolution>
   </Print>
   <Selected/>
   <Panes>
    <Pane>
     <Number>3</Number>
     <ActiveRow>1</ActiveRow>
     <ActiveCol>1</ActiveCol>
    </Pane>
   </Panes>
   <ProtectObjects>False</ProtectObjects>
   <ProtectScenarios>False</ProtectScenarios>
  </WorksheetOptions>
 </Worksheet>
</Workbook>
