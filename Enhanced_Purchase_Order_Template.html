<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchase Order - Santhigiri Format</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border: 2px solid #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 5px;
        }
        .company-details {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .po-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            text-decoration: underline;
        }
        .po-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .po-left, .po-right {
            width: 48%;
        }
        .info-box {
            border: 1px solid #333;
            padding: 10px;
            margin-bottom: 15px;
        }
        .info-title {
            font-weight: bold;
            background-color: #e6e6e6;
            padding: 5px;
            margin: -10px -10px 10px -10px;
            border-bottom: 1px solid #333;
        }
        .field-row {
            display: flex;
            margin-bottom: 8px;
        }
        .field-label {
            font-weight: bold;
            width: 120px;
            min-width: 120px;
        }
        .field-value {
            border-bottom: 1px solid #333;
            flex: 1;
            min-height: 20px;
            padding: 2px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .items-table th, .items-table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
        }
        .items-table th {
            background-color: #e6e6e6;
            font-weight: bold;
        }
        .items-table .center {
            text-align: center;
        }
        .items-table .right {
            text-align: right;
        }
        .totals-section {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
        }
        .totals-box {
            border: 1px solid #333;
            padding: 10px;
            width: 300px;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 3px 0;
        }
        .total-row.final {
            border-top: 2px solid #333;
            font-weight: bold;
            font-size: 16px;
            margin-top: 10px;
            padding-top: 8px;
        }
        .terms-section {
            margin-top: 20px;
            border: 1px solid #333;
            padding: 15px;
        }
        .signatures {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        .signature-box {
            width: 200px;
            text-align: center;
        }
        .signature-line {
            border-bottom: 1px solid #333;
            height: 50px;
            margin-bottom: 5px;
        }
        .print-button {
            background-color: #2c5aa0;
            color: white;
            padding: 10px 20px;
            border: none;
            cursor: pointer;
            margin: 20px 0;
            font-size: 16px;
        }
        @media print {
            .print-button {
                display: none;
            }
            body {
                background-color: white;
                margin: 0;
            }
            .container {
                border: none;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div class="company-name">SANTHIGIRI ASHRAM</div>
            <div class="company-details">
                Santhigiri Ashram, Pothencode P.O., Thiruvananthapuram - 695 584, Kerala, India<br>
                Phone: +91-471-2274999 | Email: <EMAIL> | Website: www.santhigiri.org<br>
                GSTIN: [GST Number] | PAN: [PAN Number]
            </div>
            <div class="po-title">PURCHASE ORDER</div>
        </div>

        <!-- Purchase Order Information -->
        <div class="po-info">
            <div class="po-left">
                <div class="info-box">
                    <div class="info-title">VENDOR DETAILS</div>
                    <div class="field-row">
                        <span class="field-label">Vendor Name:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Address:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label"></span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Phone:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Email:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">GSTIN:</span>
                        <span class="field-value"></span>
                    </div>
                </div>
            </div>
            
            <div class="po-right">
                <div class="info-box">
                    <div class="info-title">ORDER DETAILS</div>
                    <div class="field-row">
                        <span class="field-label">PO Number:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">PO Date:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Required Date:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Department:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Requested By:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Budget Code:</span>
                        <span class="field-value"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delivery Information -->
        <div class="info-box">
            <div class="info-title">DELIVERY INFORMATION</div>
            <div style="display: flex; gap: 20px;">
                <div style="flex: 1;">
                    <div class="field-row">
                        <span class="field-label">Delivery Address:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label"></span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Contact Person:</span>
                        <span class="field-value"></span>
                    </div>
                </div>
                <div style="flex: 1;">
                    <div class="field-row">
                        <span class="field-label">Delivery Date:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Delivery Time:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Special Instructions:</span>
                        <span class="field-value"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">S.No</th>
                    <th style="width: 15%;">Item Code</th>
                    <th style="width: 35%;">Description</th>
                    <th style="width: 8%;">Unit</th>
                    <th style="width: 8%;">Qty</th>
                    <th style="width: 12%;">Rate</th>
                    <th style="width: 12%;">Amount</th>
                    <th style="width: 5%;">Tax%</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="center">1</td>
                    <td></td>
                    <td></td>
                    <td class="center"></td>
                    <td class="center"></td>
                    <td class="right"></td>
                    <td class="right"></td>
                    <td class="center"></td>
                </tr>
                <tr>
                    <td class="center">2</td>
                    <td></td>
                    <td></td>
                    <td class="center"></td>
                    <td class="center"></td>
                    <td class="right"></td>
                    <td class="right"></td>
                    <td class="center"></td>
                </tr>
                <tr>
                    <td class="center">3</td>
                    <td></td>
                    <td></td>
                    <td class="center"></td>
                    <td class="center"></td>
                    <td class="right"></td>
                    <td class="right"></td>
                    <td class="center"></td>
                </tr>
                <tr>
                    <td class="center">4</td>
                    <td></td>
                    <td></td>
                    <td class="center"></td>
                    <td class="center"></td>
                    <td class="right"></td>
                    <td class="right"></td>
                    <td class="center"></td>
                </tr>
                <tr>
                    <td class="center">5</td>
                    <td></td>
                    <td></td>
                    <td class="center"></td>
                    <td class="center"></td>
                    <td class="right"></td>
                    <td class="right"></td>
                    <td class="center"></td>
                </tr>
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="totals-section">
            <div class="totals-box">
                <div class="total-row">
                    <span>Sub Total:</span>
                    <span>₹ _________</span>
                </div>
                <div class="total-row">
                    <span>Discount (%):</span>
                    <span>₹ _________</span>
                </div>
                <div class="total-row">
                    <span>Taxable Amount:</span>
                    <span>₹ _________</span>
                </div>
                <div class="total-row">
                    <span>CGST:</span>
                    <span>₹ _________</span>
                </div>
                <div class="total-row">
                    <span>SGST:</span>
                    <span>₹ _________</span>
                </div>
                <div class="total-row">
                    <span>IGST:</span>
                    <span>₹ _________</span>
                </div>
                <div class="total-row">
                    <span>Other Charges:</span>
                    <span>₹ _________</span>
                </div>
                <div class="total-row final">
                    <span>TOTAL AMOUNT:</span>
                    <span>₹ _________</span>
                </div>
            </div>
        </div>

        <!-- Terms and Conditions -->
        <div class="terms-section">
            <div style="font-weight: bold; margin-bottom: 10px;">TERMS AND CONDITIONS:</div>
            <div style="display: flex; gap: 30px;">
                <div style="flex: 1;">
                    <div>1. Payment Terms: _______________</div>
                    <div>2. Delivery Terms: _______________</div>
                    <div>3. Warranty Period: _______________</div>
                    <div>4. Quality Standards: As per specifications</div>
                    <div>5. Inspection: Subject to our inspection</div>
                </div>
                <div style="flex: 1;">
                    <div>6. Returns: Defective items returnable</div>
                    <div>7. Force Majeure: Standard clause applies</div>
                    <div>8. Jurisdiction: Thiruvananthapuram</div>
                    <div>9. Compliance: All statutory compliances required</div>
                    <div>10. Amendment: Only in writing</div>
                </div>
            </div>
        </div>

        <!-- Signatures -->
        <div class="signatures">
            <div class="signature-box">
                <div class="signature-line"></div>
                <div style="font-weight: bold;">Prepared By</div>
                <div>Name: _______________</div>
                <div>Date: _______________</div>
            </div>
            <div class="signature-box">
                <div class="signature-line"></div>
                <div style="font-weight: bold;">Approved By</div>
                <div>Name: _______________</div>
                <div>Date: _______________</div>
            </div>
            <div class="signature-box">
                <div class="signature-line"></div>
                <div style="font-weight: bold;">Authorized Signatory</div>
                <div>Name: _______________</div>
                <div>Date: _______________</div>
            </div>
        </div>

        <!-- Print Button -->
        <button class="print-button" onclick="window.print()">Print Purchase Order</button>
    </div>

    <script>
        // Add current date to PO Date field when page loads
        window.onload = function() {
            const today = new Date().toLocaleDateString('en-IN');
            // You can uncomment the line below to auto-fill today's date
            // document.querySelector('.po-right .field-value').textContent = today;
        }
    </script>
</body>
</html>
